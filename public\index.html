<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Sauna Analytics Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              sauna: {
                50: "#fef7ee",
                100: "#fdedd3",
                200: "#fbd7a5",
                300: "#f8ba6d",
                400: "#f59332",
                500: "#f2750a",
                600: "#e35d05",
                700: "#bc4508",
                800: "#95370e",
                900: "#782f0f",
              },
            },
          },
        },
      };
    </script>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">🧖‍♂️ Sauna Analytics</h1>
            <p class="text-gray-600 mt-1">
              Real-time visitor tracking and insights
            </p>
          </div>
          <div class="flex items-center space-x-4">
            <div
              class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium"
            >
              <span
                class="inline-block w-2 h-2 bg-green-400 rounded-full mr-2"
              ></span>
              Live Data
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Dashboard -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Loading State -->
      <div id="loadingState" class="flex items-center justify-center py-12">
        <div
          class="animate-spin rounded-full h-12 w-12 border-b-2 border-sauna-500"
        ></div>
        <span class="ml-3 text-gray-600">Loading sauna data...</span>
      </div>

      <!-- Error State -->
      <div
        id="errorState"
        class="hidden bg-red-50 border border-red-200 rounded-lg p-6 text-center"
      >
        <div class="text-red-600 text-lg font-medium mb-2">
          Unable to load data
        </div>
        <p class="text-red-500">
          Please check if the server is running and try refreshing the page.
        </p>
      </div>

      <!-- Dashboard Content -->
      <div id="dashboardContent" class="hidden space-y-8">
        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div
                  class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center"
                >
                  <span class="text-blue-600 text-lg">👥</span>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">
                  Avg Daily Visitors
                </p>
                <p
                  id="avgDailyVisitors"
                  class="text-2xl font-bold text-gray-900"
                >
                  -
                </p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div
                  class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center"
                >
                  <span class="text-green-600 text-lg">📈</span>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Peak Hour</p>
                <p id="peakHour" class="text-2xl font-bold text-gray-900">-</p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div
                  class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center"
                >
                  <span class="text-purple-600 text-lg">📅</span>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Days Tracked</p>
                <p id="daysTracked" class="text-2xl font-bold text-gray-900">
                  -
                </p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div
                  class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center"
                >
                  <span class="text-orange-600 text-lg">🔥</span>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">
                  Current Visitors
                </p>
                <p
                  id="currentVisitors"
                  class="text-2xl font-bold text-gray-900"
                >
                  -
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Daily Navigation and Chart -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center justify-between mb-6">
            <div>
              <h2 class="text-xl font-bold text-gray-900">
                Daily Visitor Trends
              </h2>
              <p class="text-gray-600">
                Navigate through different days to see visitor patterns
              </p>
            </div>
            <div class="flex items-center space-x-4">
              <button
                id="prevDay"
                class="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors"
              >
                <svg
                  class="w-5 h-5 text-gray-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 19l-7-7 7-7"
                  ></path>
                </svg>
              </button>
              <div class="text-center min-w-[200px]">
                <div
                  id="currentDate"
                  class="text-lg font-semibold text-gray-900"
                >
                  -
                </div>
                <div id="currentDayStats" class="text-sm text-gray-600">-</div>
              </div>
              <button
                id="nextDay"
                class="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors"
              >
                <svg
                  class="w-5 h-5 text-gray-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 5l7 7-7 7"
                  ></path>
                </svg>
              </button>
            </div>
          </div>
          <div class="h-96">
            <canvas id="dailyChart"></canvas>
          </div>
        </div>

        <!-- Peak Hours Analysis -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-bold text-gray-900 mb-4">
              Peak Hours Analysis
            </h3>
            <div class="h-64">
              <canvas id="peakHoursChart"></canvas>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-bold text-gray-900 mb-4">Weekly Pattern</h3>
            <div class="h-64">
              <canvas id="weeklyChart"></canvas>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <script src="app.js"></script>
  </body>
</html>
