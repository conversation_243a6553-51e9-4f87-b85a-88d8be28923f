# Sauna Tracker - QNAP NAS Deployment Guide

This guide will help you deploy the enhanced Sauna Analytics Dashboard on your QNAP NAS and migrate historic data from previous versions.

## 🚀 Quick Deployment

### Prerequisites
- QNAP NAS with Container Station installed
- Docker and Docker Compose support
- SSH access to your QNAP NAS

### Step 1: Prepare the Project
1. Copy the entire project folder to your QNAP NAS
2. SSH into your QNAP NAS
3. Navigate to the project directory

### Step 2: Build and Deploy
```bash
# Build the Docker image
docker-compose build

# Start the container
docker-compose up -d

# Check if it's running
docker-compose ps
```

### Step 3: Access the Dashboard
- Open your browser and go to: `http://your-qnap-ip:3000`
- The dashboard should load with the new analytics interface

## 📊 Migrating Historic Data

### From Previous Container Version
If you have historic data from a previous version of the sauna tracker:

1. **Locate your old data file:**
   ```bash
   # Find the old CSV file (usually named sauna_capacity.csv)
   find /share -name "sauna_capacity.csv" -type f 2>/dev/null
   ```

2. **Copy historic data to new container:**
   ```bash
   # Stop the new container
   docker-compose down
   
   # Copy your old CSV file to the data directory
   cp /path/to/old/sauna_capacity.csv ./data/sauna_capacity.csv
   
   # Ensure correct permissions
   chown 1001:1001 ./data/sauna_capacity.csv
   
   # Restart the container
   docker-compose up -d
   ```

3. **Verify data migration:**
   - Check the dashboard - you should see historic data
   - Navigate through different days using the arrow buttons
   - Verify statistics are calculated correctly

### Data Format Compatibility
The new version is fully compatible with the previous CSV format:
```csv
timestamp,visitors
2025-07-11T08:00:00.000Z,0
2025-07-11T08:30:00.000Z,2
...
```

## 🔧 Configuration Options

### Environment Variables
You can customize the deployment by modifying the `docker-compose.yml`:

```yaml
environment:
  - NODE_ENV=production
  - PORT=3000  # Change port if needed
```

### Port Configuration
To change the external port, modify the ports section:
```yaml
ports:
  - "8080:3000"  # Access via port 8080
```

### Data Persistence
The container uses a volume mount for data persistence:
```yaml
volumes:
  - ./data:/app/data  # Data stored in ./data directory
```

## 🏥 Health Monitoring

The container includes health checks:
```bash
# Check container health
docker-compose ps

# View health check logs
docker logs sauna-tracker
```

## 🔄 Updates and Maintenance

### Updating the Application
```bash
# Pull latest changes
git pull

# Rebuild and restart
docker-compose down
docker-compose build
docker-compose up -d
```

### Backup Data
```bash
# Create backup of data
cp -r ./data ./data-backup-$(date +%Y%m%d)

# Or create compressed backup
tar -czf sauna-data-backup-$(date +%Y%m%d).tar.gz ./data
```

### View Logs
```bash
# View real-time logs
docker-compose logs -f

# View specific service logs
docker-compose logs sauna-tracker
```

## 🛠️ Troubleshooting

### Container Won't Start
1. Check logs: `docker-compose logs`
2. Verify port availability: `netstat -tulpn | grep 3000`
3. Check file permissions: `ls -la ./data`

### Data Not Showing
1. Verify CSV file exists: `ls -la ./data/sauna_capacity.csv`
2. Check file format and encoding
3. Restart container: `docker-compose restart`

### Permission Issues
```bash
# Fix data directory permissions
sudo chown -R 1001:1001 ./data
```

## 📈 Features Available After Deployment

✅ **Daily Navigation** - Browse through different days
✅ **Real-time Statistics** - Average visitors, peak hours, etc.
✅ **Multiple Charts** - Daily trends, peak hours, weekly patterns
✅ **Responsive Design** - Works on mobile and desktop
✅ **Auto-refresh** - Updates every 5 minutes
✅ **Health Monitoring** - Built-in health checks

## 🔐 Security Notes

- Container runs as non-root user (sauna:1001)
- Only port 3000 is exposed
- Data directory has restricted permissions
- Health checks ensure service availability

## 📞 Support

If you encounter issues:
1. Check the logs: `docker-compose logs`
2. Verify data file format
3. Ensure proper file permissions
4. Check network connectivity

The new dashboard provides much richer insights into your sauna usage patterns while maintaining full compatibility with your existing data!
