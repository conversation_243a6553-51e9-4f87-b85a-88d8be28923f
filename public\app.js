// Global state
let allData = [];
let dailyData = {};
let availableDates = [];
let currentDateIndex = 0;
let charts = {};

// DOM elements
const loadingState = document.getElementById("loadingState");
const errorState = document.getElementById("errorState");
const dashboardContent = document.getElementById("dashboardContent");

// Statistics elements
const avgDailyVisitors = document.getElementById("avgDailyVisitors");
const peakHour = document.getElementById("peakHour");
const daysTracked = document.getElementById("daysTracked");
const currentVisitors = document.getElementById("currentVisitors");

// Navigation elements
const prevDayBtn = document.getElementById("prevDay");
const nextDayBtn = document.getElementById("nextDay");
const currentDate = document.getElementById("currentDate");
const currentDayStats = document.getElementById("currentDayStats");

// Utility functions
function formatDate(date) {
  return date.toLocaleDateString("en-US", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  });
}

function formatTime(date) {
  return date.toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  });
}

function getDateKey(date) {
  return date.toISOString().split("T")[0];
}

// Data processing functions
function processData(csvData) {
  const rows = csvData.trim().split("\n");
  rows.shift(); // Remove header row

  const data = rows.map((row) => {
    const [timestamp, visitors] = row.split(",");
    return {
      timestamp: new Date(timestamp),
      visitors: parseInt(visitors, 10),
      dateKey: getDateKey(new Date(timestamp)),
    };
  });

  // Group data by date
  const grouped = {};
  data.forEach((entry) => {
    if (!grouped[entry.dateKey]) {
      grouped[entry.dateKey] = [];
    }
    grouped[entry.dateKey].push(entry);
  });

  // Sort dates and create available dates array
  const dates = Object.keys(grouped).sort();

  return {
    allData: data,
    dailyData: grouped,
    availableDates: dates,
  };
}

function calculateStatistics(data, dailyData) {
  if (data.length === 0) return {};

  // Calculate average daily visitors (only for days with data)
  const dailyTotals = Object.values(dailyData).map((dayData) => {
    return Math.max(...dayData.map((entry) => entry.visitors));
  });
  const avgDaily =
    dailyTotals.reduce((sum, total) => sum + total, 0) / dailyTotals.length;

  // Find peak hour across all data
  const hourCounts = {};
  data.forEach((entry) => {
    const hour = entry.timestamp.getHours();
    if (!hourCounts[hour]) hourCounts[hour] = [];
    hourCounts[hour].push(entry.visitors);
  });

  let peakHourValue = 0;
  let peakHourTime = 0;
  Object.entries(hourCounts).forEach(([hour, visitors]) => {
    const avgVisitors =
      visitors.reduce((sum, v) => sum + v, 0) / visitors.length;
    if (avgVisitors > peakHourValue) {
      peakHourValue = avgVisitors;
      peakHourTime = parseInt(hour);
    }
  });

  // Current visitors (latest entry)
  const latest = data[data.length - 1];

  return {
    avgDaily: Math.round(avgDaily * 10) / 10,
    peakHour: peakHourTime,
    daysTracked: Object.keys(dailyData).length,
    currentVisitors: latest ? latest.visitors : 0,
    hourCounts,
  };
}

// Chart rendering functions
function renderDailyChart(dateKey) {
  const ctx = document.getElementById("dailyChart").getContext("2d");

  if (charts.daily) {
    charts.daily.destroy();
  }

  const dayData = dailyData[dateKey] || [];
  const chartData = dayData.map((entry) => ({
    x: entry.timestamp,
    y: entry.visitors,
  }));

  charts.daily = new Chart(ctx, {
    type: "line",
    data: {
      datasets: [
        {
          label: "Visitors",
          data: chartData,
          borderColor: "#f2750a",
          backgroundColor: "rgba(242, 117, 10, 0.1)",
          borderWidth: 3,
          pointRadius: 4,
          pointHoverRadius: 6,
          fill: true,
          tension: 0.2,
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false,
        },
      },
      scales: {
        x: {
          type: "time",
          time: {
            unit: "hour",
            tooltipFormat: "MMM d, h:mm a",
            displayFormats: {
              hour: "h:mm a",
            },
          },
          title: {
            display: true,
            text: "Time of Day",
            font: { size: 14, weight: "bold" },
          },
          grid: {
            color: "rgba(0, 0, 0, 0.1)",
          },
        },
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: "Number of Visitors",
            font: { size: 14, weight: "bold" },
          },
          grid: {
            color: "rgba(0, 0, 0, 0.1)",
          },
        },
      },
      interaction: {
        intersect: false,
        mode: "index",
      },
    },
  });
}

function renderPeakHoursChart(hourCounts) {
  const ctx = document.getElementById("peakHoursChart").getContext("2d");

  if (charts.peakHours) {
    charts.peakHours.destroy();
  }

  // Prepare data for all 24 hours
  const hours = Array.from({ length: 24 }, (_, i) => i);
  const avgVisitors = hours.map((hour) => {
    if (hourCounts[hour]) {
      return (
        hourCounts[hour].reduce((sum, v) => sum + v, 0) /
        hourCounts[hour].length
      );
    }
    return 0;
  });

  const labels = hours.map((hour) => {
    const date = new Date();
    date.setHours(hour, 0, 0, 0);
    return date.toLocaleTimeString("en-US", { hour: "2-digit", hour12: true });
  });

  charts.peakHours = new Chart(ctx, {
    type: "bar",
    data: {
      labels: labels,
      datasets: [
        {
          label: "Average Visitors",
          data: avgVisitors,
          backgroundColor: "rgba(34, 197, 94, 0.8)",
          borderColor: "rgb(34, 197, 94)",
          borderWidth: 1,
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false,
        },
      },
      scales: {
        x: {
          title: {
            display: true,
            text: "Hour of Day",
            font: { size: 12, weight: "bold" },
          },
        },
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: "Avg Visitors",
            font: { size: 12, weight: "bold" },
          },
        },
      },
    },
  });
}

function renderWeeklyChart(dailyData) {
  const ctx = document.getElementById("weeklyChart").getContext("2d");

  if (charts.weekly) {
    charts.weekly.destroy();
  }

  // Group data by day of week
  const weeklyData = { 0: [], 1: [], 2: [], 3: [], 4: [], 5: [], 6: [] };

  Object.entries(dailyData).forEach(([dateKey, dayData]) => {
    const date = new Date(dateKey);
    const dayOfWeek = date.getDay();
    const maxVisitors = Math.max(...dayData.map((entry) => entry.visitors));
    weeklyData[dayOfWeek].push(maxVisitors);
  });

  const dayNames = [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ];
  const avgByDay = dayNames.map((_, index) => {
    const dayData = weeklyData[index];
    if (dayData.length === 0) return 0;
    return (
      dayData.reduce((sum, visitors) => sum + visitors, 0) / dayData.length
    );
  });

  charts.weekly = new Chart(ctx, {
    type: "bar",
    data: {
      labels: dayNames,
      datasets: [
        {
          label: "Average Peak Visitors",
          data: avgByDay,
          backgroundColor: "rgba(147, 51, 234, 0.8)",
          borderColor: "rgb(147, 51, 234)",
          borderWidth: 1,
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false,
        },
      },
      scales: {
        x: {
          title: {
            display: true,
            text: "Day of Week",
            font: { size: 12, weight: "bold" },
          },
        },
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: "Avg Peak Visitors",
            font: { size: 12, weight: "bold" },
          },
        },
      },
    },
  });
}

// UI update functions
function updateStatistics(stats) {
  avgDailyVisitors.textContent = stats.avgDaily || "-";
  peakHour.textContent =
    stats.peakHour !== undefined ? `${stats.peakHour}:00` : "-";
  daysTracked.textContent = stats.daysTracked || "-";
  currentVisitors.textContent = stats.currentVisitors || "-";
}

function updateDateNavigation() {
  if (availableDates.length === 0) return;

  const dateKey = availableDates[currentDateIndex];
  const date = new Date(dateKey);
  const dayData = dailyData[dateKey];

  currentDate.textContent = formatDate(date);

  if (dayData && dayData.length > 0) {
    const maxVisitors = Math.max(...dayData.map((entry) => entry.visitors));
    const minVisitors = Math.min(...dayData.map((entry) => entry.visitors));
    const dataPoints = dayData.length;
    currentDayStats.textContent = `${dataPoints} readings • Peak: ${maxVisitors} • Low: ${minVisitors}`;
  } else {
    currentDayStats.textContent = "No data available";
  }

  // Update button states
  prevDayBtn.disabled = currentDateIndex === 0;
  nextDayBtn.disabled = currentDateIndex === availableDates.length - 1;

  // Update button styles
  prevDayBtn.className =
    currentDateIndex === 0
      ? "p-2 rounded-lg border border-gray-300 bg-gray-100 text-gray-400 cursor-not-allowed"
      : "p-2 rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors text-gray-600";

  nextDayBtn.className =
    currentDateIndex === availableDates.length - 1
      ? "p-2 rounded-lg border border-gray-300 bg-gray-100 text-gray-400 cursor-not-allowed"
      : "p-2 rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors text-gray-600";
}

// Event handlers
function setupEventHandlers() {
  prevDayBtn.addEventListener("click", () => {
    if (currentDateIndex > 0) {
      currentDateIndex--;
      updateDateNavigation();
      renderDailyChart(availableDates[currentDateIndex]);
    }
  });

  nextDayBtn.addEventListener("click", () => {
    if (currentDateIndex < availableDates.length - 1) {
      currentDateIndex++;
      updateDateNavigation();
      renderDailyChart(availableDates[currentDateIndex]);
    }
  });
}

// Main application functions
async function fetchDataAndInitialize() {
  try {
    loadingState.classList.remove("hidden");
    errorState.classList.add("hidden");
    dashboardContent.classList.add("hidden");

    const response = await fetch("/data");
    if (!response.ok) {
      throw new Error("Network response was not ok");
    }

    const csvData = await response.text();
    const processedData = processData(csvData);

    // Update global state
    allData = processedData.allData;
    dailyData = processedData.dailyData;
    availableDates = processedData.availableDates;

    if (availableDates.length === 0) {
      throw new Error("No data available");
    }

    // Set current date to the latest available date
    currentDateIndex = availableDates.length - 1;

    // Calculate statistics
    const stats = calculateStatistics(allData, dailyData);

    // Update UI
    updateStatistics(stats);
    updateDateNavigation();

    // Render charts
    renderDailyChart(availableDates[currentDateIndex]);
    renderPeakHoursChart(stats.hourCounts);
    renderWeeklyChart(dailyData);

    // Show dashboard
    loadingState.classList.add("hidden");
    dashboardContent.classList.remove("hidden");
  } catch (error) {
    console.error("Failed to fetch or process data:", error);
    loadingState.classList.add("hidden");
    errorState.classList.remove("hidden");
  }
}

// Initialize the application
document.addEventListener("DOMContentLoaded", () => {
  setupEventHandlers();
  fetchDataAndInitialize();

  // Set up auto-refresh every 5 minutes
  setInterval(fetchDataAndInitialize, 5 * 60 * 1000);
});
