﻿# --- Builder Stage ---
FROM node:18-alpine AS builder
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev dependencies for building)
RUN npm install

# Copy source code
COPY . .

# Build the TypeScript code
RUN npm run build

# --- Production Stage ---
FROM node:18-alpine
WORKDIR /app

# Install wget for health checks
RUN apk add --no-cache wget

# Copy package files
COPY package*.json ./

# Install only production dependencies
RUN npm ci --only=production

# Copy the compiled JS code from the 'builder' stage
COPY --from=builder /app/dist ./dist

# Copy the static frontend files from the 'builder' stage
COPY --from=builder /app/public ./public

# Create data directory for persistent storage
RUN mkdir -p /app/data

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S sauna -u 1001 -G nodejs

# Change ownership of app directory to sauna user
RUN chown -R sauna:nodejs /app

# Switch to non-root user
USER sauna

# Expose port
EXPOSE 3000

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:3000/ || exit 1

# Create volume for data persistence
VOLUME ["/app/data"]

# Start the application
CMD ["npm", "start"]
