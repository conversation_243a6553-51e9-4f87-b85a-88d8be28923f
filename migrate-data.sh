#!/bin/bash

# Sauna Tracker Data Migration Script
# This script helps migrate historic data from previous container versions

set -e

echo "🧖‍♂️ Sauna Tracker Data Migration Script"
echo "========================================"

# Function to find old data files
find_old_data() {
    echo "🔍 Searching for existing sauna data files..."
    
    # Common locations where old data might be stored
    SEARCH_PATHS=(
        "/share/Container"
        "/share/Docker"
        "/share/homes"
        "/opt/sauna-tracker"
        "/var/lib/docker/volumes"
        "."
    )
    
    FOUND_FILES=()
    
    for path in "${SEARCH_PATHS[@]}"; do
        if [ -d "$path" ]; then
            echo "  Searching in: $path"
            while IFS= read -r -d '' file; do
                FOUND_FILES+=("$file")
            done < <(find "$path" -name "sauna_capacity.csv" -type f -print0 2>/dev/null || true)
        fi
    done
    
    if [ ${#FOUND_FILES[@]} -eq 0 ]; then
        echo "❌ No existing sauna data files found."
        echo "   If you have data in a different location, please specify the path manually."
        return 1
    fi
    
    echo "✅ Found ${#FOUND_FILES[@]} data file(s):"
    for i in "${!FOUND_FILES[@]}"; do
        echo "  $((i+1)). ${FOUND_FILES[$i]}"
        # Show file info
        if [ -f "${FOUND_FILES[$i]}" ]; then
            lines=$(wc -l < "${FOUND_FILES[$i]}" 2>/dev/null || echo "unknown")
            size=$(du -h "${FOUND_FILES[$i]}" 2>/dev/null | cut -f1 || echo "unknown")
            echo "     Lines: $lines, Size: $size"
        fi
    done
    
    return 0
}

# Function to validate CSV format
validate_csv() {
    local file="$1"
    echo "🔍 Validating CSV format: $file"
    
    if [ ! -f "$file" ]; then
        echo "❌ File does not exist: $file"
        return 1
    fi
    
    # Check if file has header
    header=$(head -n 1 "$file")
    if [[ "$header" != "timestamp,visitors" ]]; then
        echo "⚠️  Warning: Header might be incorrect. Expected 'timestamp,visitors', found: '$header'"
    fi
    
    # Check a few data lines
    echo "📊 Sample data (first 3 lines after header):"
    tail -n +2 "$file" | head -n 3 | while read -r line; do
        echo "   $line"
    done
    
    # Count total records
    total_lines=$(tail -n +2 "$file" | wc -l)
    echo "📈 Total data records: $total_lines"
    
    return 0
}

# Function to backup existing data
backup_existing() {
    if [ -f "./data/sauna_capacity.csv" ]; then
        backup_name="./data/sauna_capacity.csv.backup.$(date +%Y%m%d_%H%M%S)"
        echo "💾 Backing up existing data to: $backup_name"
        cp "./data/sauna_capacity.csv" "$backup_name"
        echo "✅ Backup created successfully"
    fi
}

# Function to migrate data
migrate_data() {
    local source_file="$1"
    
    echo "🚀 Starting data migration..."
    
    # Create data directory if it doesn't exist
    mkdir -p ./data
    
    # Backup existing data
    backup_existing
    
    # Copy the data file
    echo "📋 Copying data file..."
    cp "$source_file" "./data/sauna_capacity.csv"
    
    # Set correct permissions (user ID 1001 as defined in Dockerfile)
    echo "🔐 Setting correct permissions..."
    chown 1001:1001 "./data/sauna_capacity.csv" 2>/dev/null || {
        echo "⚠️  Could not set ownership to 1001:1001. This might be okay if running as non-root."
        chmod 644 "./data/sauna_capacity.csv"
    }
    
    echo "✅ Data migration completed successfully!"
    echo "📊 Data file location: ./data/sauna_capacity.csv"
}

# Main script execution
main() {
    echo ""
    
    # Check if data directory exists
    if [ ! -d "./data" ]; then
        echo "📁 Creating data directory..."
        mkdir -p ./data
    fi
    
    # Check if we're in the right directory
    if [ ! -f "docker-compose.yml" ]; then
        echo "❌ Error: docker-compose.yml not found. Please run this script from the sauna-tracker project directory."
        exit 1
    fi
    
    # Option 1: Auto-find data files
    echo "Choose migration option:"
    echo "1. Auto-search for existing data files"
    echo "2. Specify data file path manually"
    echo "3. Skip migration (use current data)"
    echo ""
    read -p "Enter your choice (1-3): " choice
    
    case $choice in
        1)
            if find_old_data; then
                echo ""
                read -p "Enter the number of the file to migrate (1-${#FOUND_FILES[@]}): " file_choice
                if [[ "$file_choice" =~ ^[0-9]+$ ]] && [ "$file_choice" -ge 1 ] && [ "$file_choice" -le ${#FOUND_FILES[@]} ]; then
                    selected_file="${FOUND_FILES[$((file_choice-1))]}"
                    validate_csv "$selected_file"
                    echo ""
                    read -p "Proceed with migration? (y/N): " confirm
                    if [[ "$confirm" =~ ^[Yy]$ ]]; then
                        migrate_data "$selected_file"
                    else
                        echo "❌ Migration cancelled."
                        exit 0
                    fi
                else
                    echo "❌ Invalid selection."
                    exit 1
                fi
            else
                echo "Please try option 2 to specify the path manually."
                exit 1
            fi
            ;;
        2)
            read -p "Enter the full path to your CSV file: " manual_path
            if validate_csv "$manual_path"; then
                echo ""
                read -p "Proceed with migration? (y/N): " confirm
                if [[ "$confirm" =~ ^[Yy]$ ]]; then
                    migrate_data "$manual_path"
                else
                    echo "❌ Migration cancelled."
                    exit 0
                fi
            else
                echo "❌ File validation failed."
                exit 1
            fi
            ;;
        3)
            echo "⏭️  Skipping migration. Using current data."
            ;;
        *)
            echo "❌ Invalid choice."
            exit 1
            ;;
    esac
    
    echo ""
    echo "🎉 Migration process completed!"
    echo ""
    echo "Next steps:"
    echo "1. Build and start the container: docker-compose up -d"
    echo "2. Access the dashboard: http://your-qnap-ip:3000"
    echo "3. Verify your historic data is displayed correctly"
    echo ""
    echo "For troubleshooting, check: docker-compose logs"
}

# Run main function
main "$@"
