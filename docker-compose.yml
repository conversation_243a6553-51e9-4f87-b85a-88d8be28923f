version: '3.8'

services:
  sauna-tracker:
    build: .
    container_name: sauna-tracker
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      # Mount data directory for persistence
      - ./data:/app/data
      # Alternative: use named volume (uncomment if preferred)
      # - sauna-data:/app/data
    environment:
      - NODE_ENV=production
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - sauna-network

networks:
  sauna-network:
    driver: bridge

# Uncomment if using named volume instead of bind mount
# volumes:
#   sauna-data:
#     driver: local
