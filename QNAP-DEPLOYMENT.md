# QNAP Container Station Deployment Guide

## 🎯 Quick Deployment Steps

### Step 1: Access Container Station
1. Open your QNAP NAS web interface
2. Go to **App Center** → Install **Container Station** (if not already installed)
3. Open **Container Station**

### Step 2: Create Container from Docker Hub Image

#### Option A: Using Container Station GUI
1. Click **"Create"** → **"Create Container"**
2. In the **"Image"** field, enter: `yourusername/sauna-tracker:latest`
   (Replace `yourusername` with your Docker Hub username)
3. Click **"Next"**

#### Option B: Search and Pull Image
1. Go to **"Images"** tab
2. Click **"Pull"**
3. Search for: `yourusername/sauna-tracker`
4. Pull the `latest` tag
5. Once pulled, click **"Create Container"**

### Step 3: Container Configuration

#### Basic Settings
- **Container Name**: `sauna-tracker`
- **Image**: `yourusername/sauna-tracker:latest`
- **CPU Limit**: 1 (or as needed)
- **Memory Limit**: 512MB (or as needed)

#### Network Settings
- **Network Mode**: Bridge
- **Port Settings**:
  - **Host Port**: `3000` (or any available port you prefer)
  - **Container Port**: `3000`
  - **Protocol**: TCP

#### Volume Settings (IMPORTANT for data persistence)
Click **"Advanced Settings"** → **"Volume"** → **"Add Volume"**

**Volume Mapping 1 - Data Directory:**
- **Host Path**: `/share/Container/sauna-tracker-data` (or create your preferred path)
- **Container Path**: `/app/data`
- **Permission**: Read/Write

**Optional Volume Mapping 2 - Logs:**
- **Host Path**: `/share/Container/sauna-tracker-logs`
- **Container Path**: `/app/logs`
- **Permission**: Read/Write

#### Environment Variables (Optional)
- **NODE_ENV**: `production`
- **PORT**: `3000`

### Step 4: Migrate Historic Data

#### Before Starting the Container:
1. **SSH into your QNAP NAS** or use **File Station**
2. **Navigate to the data directory**: `/share/Container/sauna-tracker-data`
3. **Copy your old CSV file**:
   ```bash
   # If you have old data, copy it to the new location
   cp /path/to/old/sauna_capacity.csv /share/Container/sauna-tracker-data/sauna_capacity.csv
   ```
4. **Set correct permissions**:
   ```bash
   chown 1001:1001 /share/Container/sauna-tracker-data/sauna_capacity.csv
   chmod 644 /share/Container/sauna-tracker-data/sauna_capacity.csv
   ```

### Step 5: Start the Container
1. Click **"Create"** to create the container
2. The container should start automatically
3. Check the **"Status"** - it should show as **"Running"**

### Step 6: Access Your Dashboard
1. Open your browser
2. Go to: `http://your-qnap-ip:3000`
3. You should see the new Sauna Analytics Dashboard!

## 🔧 Configuration Examples

### Complete Container Creation Command (CLI Alternative)
If you prefer using SSH and Docker commands:

```bash
# Create data directory
mkdir -p /share/Container/sauna-tracker-data

# Run container
docker run -d \
  --name sauna-tracker \
  --restart unless-stopped \
  -p 3000:3000 \
  -v /share/Container/sauna-tracker-data:/app/data \
  yourusername/sauna-tracker:latest
```

### Docker Compose on QNAP (Advanced)
If your QNAP supports docker-compose:

```yaml
version: '3.8'
services:
  sauna-tracker:
    image: yourusername/sauna-tracker:latest
    container_name: sauna-tracker
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - /share/Container/sauna-tracker-data:/app/data
    environment:
      - NODE_ENV=production
```

## 🔍 Troubleshooting

### Container Won't Start
1. **Check logs**: Container Station → Containers → sauna-tracker → Logs
2. **Common issues**:
   - Port 3000 already in use → Change host port to 3001, 8080, etc.
   - Permission issues → Check volume permissions
   - Image pull failed → Verify Docker Hub image name

### Can't Access Dashboard
1. **Check container status**: Should be "Running"
2. **Verify port mapping**: Host port should be accessible
3. **Check firewall**: Ensure port is open on QNAP
4. **Test locally**: SSH to QNAP and run `curl http://localhost:3000`

### Data Not Showing
1. **Check data file**: Verify `/share/Container/sauna-tracker-data/sauna_capacity.csv` exists
2. **Check file format**: Should have header `timestamp,visitors`
3. **Check permissions**: File should be readable by container user (1001)

### Health Check Failed
1. **Wait 30-60 seconds** for container to fully start
2. **Check logs** for any startup errors
3. **Verify dependencies** are properly installed

## 📊 Expected Results

After successful deployment, you should see:
- ✅ Modern dashboard with Tailwind CSS styling
- ✅ Daily navigation with arrow buttons
- ✅ Statistics cards (avg visitors, peak hour, etc.)
- ✅ Multiple charts (daily trends, peak hours, weekly patterns)
- ✅ Historic data properly migrated and displayed
- ✅ Auto-refresh every 5 minutes
- ✅ Responsive design for mobile/desktop

## 🔄 Updates

To update to a newer version:
1. **Pull new image**: `docker pull yourusername/sauna-tracker:latest`
2. **Stop old container**: Container Station → Stop
3. **Remove old container**: Container Station → Remove
4. **Create new container** with same settings
5. **Data persists** through volume mapping

## 📞 Support

If you encounter issues:
1. Check container logs in Container Station
2. Verify volume mappings are correct
3. Ensure data file format is compatible
4. Check QNAP system resources (CPU, memory)

Your historic data will be preserved and the new dashboard provides much richer analytics!
